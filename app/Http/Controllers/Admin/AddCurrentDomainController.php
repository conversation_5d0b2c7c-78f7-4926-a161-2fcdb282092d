<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Protocol;
use App\Http\Controllers\Controller;
use App\Models\Domain;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;

class AddCurrentDomainController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            $host = $request->getHttpHost();
            $protocol = $request->getScheme();

            // Convert string protocol to Protocol enum
            $protocolEnum = $protocol === 'https' ? Protocol::HTTPS : Protocol::HTTP;

            Domain::create([
                'host' => $host,
                'protocol' => $protocolEnum,
                'is_active' => true,
                'is_admin_panel_available' => true,
            ]);

            Notification::make()
                ->success()
                ->title("Domain '$host' added successfully.")
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to add domain')
                ->body('An error occurred: ' . $e->getMessage())
                ->send();
        }

        // Redirect back to the dashboard
        return redirect()->route('filament.admin.pages.dashboard');
    }
}
